---
title: Homework 3
date: last-modified
author:
    - name: <PERSON><PERSON>
      email: <EMAIL>
---

Link to my GITHUB Repo: <https://github.com/cmsc-vcu/cmsc408-fa2025-hw3-<PERSON>ayDharamsi>

## Introduction

This homework assignment focuses on Entity-Relationship (ER) modeling, a fundamental technique in database design. The goal is to create three unique real-world scenarios and model them using both Chen notation and Crow's Foot notation, demonstrating a comprehensive understanding of database design principles.

For each scenario, I will:
- Provide a detailed description of the system being modeled
- Identify key entities, attributes, and relationships
- Create Chen ER diagrams using Graphviz
- Create <PERSON>'s Foot ER diagrams using Mermaid
- Develop relational schemas from the ER models
- Document and justify design decisions

The three scenarios I have chosen represent different domains: library management, food delivery services, and university course registration. Each scenario presents unique modeling challenges and demonstrates different aspects of ER design, including various cardinality relationships, participation constraints, and attribute types.



# Models

Below are three entity-relation models for systems in the world around me.

## Model 1 - Library Management System

Consider a library management system that needs to track books, library members, authors, and loan transactions. The system manages a collection of books, each written by one or more authors. Library members can borrow multiple books, and each book can be borrowed by different members over time (but only one member at a time). The system needs to track when books are borrowed and returned, including due dates and potential late fees.

Each book has a unique ISBN, title, publication year, and genre. Authors have a unique author ID, name, birth year, and nationality. Library members have a member ID, name, email, phone number, and membership start date. Loan transactions record which member borrowed which book, along with the loan date, due date, return date (if returned), and any late fees incurred.

### Design choices

I chose four main entities for this system: **Book**, **Author**, **Member**, and **Loan**.

**Entity Selection Rationale:**
- **Book**: Represents the core resource being managed, with attributes like ISBN (primary key), title, publication year, and genre
- **Author**: Separate entity because authors can write multiple books and books can have multiple authors (many-to-many relationship)
- **Member**: Represents library users who can borrow books, with unique member ID and contact information
- **Loan**: Represents the transaction of borrowing a book, including temporal information like loan date, due date, and return date

**Relationship Design:**
- **Writes** (Author-Book): Many-to-many relationship since authors can write multiple books and books can have multiple authors. Total participation on Book side (every book must have at least one author), partial participation on Author side (authors may not have books in this library's collection)
- **Borrows** (Member-Book through Loan): One-to-many from Member to Loan, and many-to-one from Loan to Book. This allows tracking of loan history while ensuring a book can only be borrowed by one member at a time
- **Participation**: Members have partial participation (not all members have active loans), Books have partial participation (not all books are currently loaned), but Loans have total participation with both Member and Book

### Chen Diagram

```{dot}
//| fig-width: 12
//| fig-height: 8
digraph ER {
    rankdir=TB;
    node [shape=box, style=rounded];

    // Entities
    Book [shape=box, label="Book"];
    Author [shape=box, label="Author"];
    Member [shape=box, label="Member"];
    Loan [shape=box, label="Loan"];

    // Attributes for Book
    ISBN [shape=ellipse, label="ISBN"];
    Title [shape=ellipse, label="Title"];
    PubYear [shape=ellipse, label="Publication Year"];
    Genre [shape=ellipse, label="Genre"];

    // Attributes for Author
    AuthorID [shape=ellipse, label="Author ID"];
    AuthorName [shape=ellipse, label="Name"];
    BirthYear [shape=ellipse, label="Birth Year"];
    Nationality [shape=ellipse, label="Nationality"];

    // Attributes for Member
    MemberID [shape=ellipse, label="Member ID"];
    MemberName [shape=ellipse, label="Name"];
    Email [shape=ellipse, label="Email"];
    Phone [shape=ellipse, label="Phone"];
    StartDate [shape=ellipse, label="Start Date"];

    // Attributes for Loan
    LoanID [shape=ellipse, label="Loan ID"];
    LoanDate [shape=ellipse, label="Loan Date"];
    DueDate [shape=ellipse, label="Due Date"];
    ReturnDate [shape=ellipse, label="Return Date"];
    LateFee [shape=ellipse, label="Late Fee"];

    // Relationships
    Writes [shape=diamond, label="Writes"];
    Borrows [shape=diamond, label="Borrows"];

    // Entity-Attribute connections
    Book -> ISBN;
    Book -> Title;
    Book -> PubYear;
    Book -> Genre;

    Author -> AuthorID;
    Author -> AuthorName;
    Author -> BirthYear;
    Author -> Nationality;

    Member -> MemberID;
    Member -> MemberName;
    Member -> Email;
    Member -> Phone;
    Member -> StartDate;

    Loan -> LoanID;
    Loan -> LoanDate;
    Loan -> DueDate;
    Loan -> ReturnDate;
    Loan -> LateFee;

    // Entity-Relationship connections
    Author -> Writes [label="M"];
    Writes -> Book [label="N"];

    Member -> Borrows [label="1"];
    Borrows -> Loan [label="M"];
    Book -> Loan [label="1"];

    // Primary key indicators (underlined)
    ISBN [style="filled", fillcolor="lightgray"];
    AuthorID [style="filled", fillcolor="lightgray"];
    MemberID [style="filled", fillcolor="lightgray"];
    LoanID [style="filled", fillcolor="lightgray"];
}
```

### Crows Foot diagram

```{mermaid}
erDiagram
    BOOK {
        string ISBN PK
        string Title
        int Publication_Year
        string Genre
    }

    AUTHOR {
        int Author_ID PK
        string Name
        int Birth_Year
        string Nationality
    }

    MEMBER {
        int Member_ID PK
        string Name
        string Email
        string Phone
        date Start_Date
    }

    LOAN {
        int Loan_ID PK
        date Loan_Date
        date Due_Date
        date Return_Date
        decimal Late_Fee
        int Member_ID FK
        string Book_ISBN FK
    }

    AUTHOR ||--o{ BOOK : "writes"
    MEMBER ||--o{ LOAN : "makes"
    BOOK ||--o{ LOAN : "involved_in"
```

### Relational model

The ER model translates to the following relational schema:

- **Book**(ISBN, Title, Publication_Year, Genre)
- **Author**(Author_ID, Name, Birth_Year, Nationality)
- **Member**(Member_ID, Name, Email, Phone, Start_Date)
- **Loan**(Loan_ID, Loan_Date, Due_Date, Return_Date, Late_Fee, Member_ID, Book_ISBN)
- **Writes**(Author_ID, ISBN)

**Foreign Key Constraints:**
- Loan.Member_ID references Member.Member_ID
- Loan.Book_ISBN references Book.ISBN
- Writes.Author_ID references Author.Author_ID
- Writes.ISBN references Book.ISBN

## Model 2 - Online Food Delivery System

Consider an online food delivery platform that connects customers with restaurants and manages the delivery process through drivers. The system handles restaurant listings, customer orders, menu items, and delivery logistics. Customers can browse restaurants, view menus, place orders, and track deliveries. Restaurants manage their menus and receive orders, while drivers are assigned to deliver orders to customers.

Each restaurant has a unique restaurant ID, name, address, phone number, cuisine type, and rating. Menu items belong to specific restaurants and have item IDs, names, descriptions, prices, and categories. Customers have customer IDs, names, email addresses, phone numbers, and delivery addresses. Drivers have driver IDs, names, phone numbers, vehicle information, and current availability status. Orders contain order IDs, order timestamps, delivery addresses, total amounts, and status information. Each order can contain multiple menu items with specified quantities.

### Design choices

I identified five main entities for this system: **Restaurant**, **MenuItem**, **Customer**, **Driver**, **Order**, and **OrderItem**.

**Entity Selection Rationale:**
- **Restaurant**: Central business entity that offers food services, with attributes for identification and business information
- **MenuItem**: Represents individual food items with pricing and descriptions, belonging to specific restaurants
- **Customer**: Users who place orders, requiring contact and delivery information
- **Driver**: Delivery personnel who transport orders, with vehicle and availability information
- **Order**: Transaction entity that captures the ordering event with temporal and delivery information
- **OrderItem**: Junction entity that resolves the many-to-many relationship between Orders and MenuItems, including quantity

**Relationship Design:**
- **Offers** (Restaurant-MenuItem): One-to-many relationship since each menu item belongs to exactly one restaurant, but restaurants can have many menu items. Total participation on MenuItem side (every menu item must belong to a restaurant)
- **Places** (Customer-Order): One-to-many relationship since customers can place multiple orders, but each order belongs to one customer. Partial participation on Customer side (customers may not have placed orders yet)
- **Delivers** (Driver-Order): One-to-many relationship since drivers can deliver multiple orders, but each order is delivered by one driver. Partial participation on both sides (drivers may not be assigned orders, orders may not be assigned drivers yet)
- **Contains** (Order-MenuItem through OrderItem): Many-to-many relationship resolved through OrderItem entity, allowing orders to contain multiple menu items with quantities

### Chen Diagram

```{dot}
//| fig-width: 14
//| fig-height: 10
digraph ER {
    rankdir=TB;
    node [shape=box, style=rounded];

    // Entities
    Restaurant [shape=box, label="Restaurant"];
    MenuItem [shape=box, label="MenuItem"];
    Customer [shape=box, label="Customer"];
    Driver [shape=box, label="Driver"];
    Order [shape=box, label="Order"];
    OrderItem [shape=box, label="OrderItem"];

    // Attributes for Restaurant
    RestaurantID [shape=ellipse, label="Restaurant ID"];
    RestName [shape=ellipse, label="Name"];
    RestAddress [shape=ellipse, label="Address"];
    RestPhone [shape=ellipse, label="Phone"];
    CuisineType [shape=ellipse, label="Cuisine Type"];
    Rating [shape=ellipse, label="Rating"];

    // Attributes for MenuItem
    ItemID [shape=ellipse, label="Item ID"];
    ItemName [shape=ellipse, label="Name"];
    Description [shape=ellipse, label="Description"];
    Price [shape=ellipse, label="Price"];
    Category [shape=ellipse, label="Category"];

    // Attributes for Customer
    CustomerID [shape=ellipse, label="Customer ID"];
    CustName [shape=ellipse, label="Name"];
    CustEmail [shape=ellipse, label="Email"];
    CustPhone [shape=ellipse, label="Phone"];
    DeliveryAddress [shape=ellipse, label="Delivery Address"];

    // Attributes for Driver
    DriverID [shape=ellipse, label="Driver ID"];
    DriverName [shape=ellipse, label="Name"];
    DriverPhone [shape=ellipse, label="Phone"];
    VehicleInfo [shape=ellipse, label="Vehicle Info"];
    AvailabilityStatus [shape=ellipse, label="Availability"];

    // Attributes for Order
    OrderID [shape=ellipse, label="Order ID"];
    OrderTime [shape=ellipse, label="Order Time"];
    OrderAddress [shape=ellipse, label="Delivery Address"];
    TotalAmount [shape=ellipse, label="Total Amount"];
    OrderStatus [shape=ellipse, label="Status"];

    // Attributes for OrderItem
    Quantity [shape=ellipse, label="Quantity"];

    // Relationships
    Offers [shape=diamond, label="Offers"];
    Places [shape=diamond, label="Places"];
    Delivers [shape=diamond, label="Delivers"];
    Contains [shape=diamond, label="Contains"];

    // Entity-Attribute connections
    Restaurant -> RestaurantID;
    Restaurant -> RestName;
    Restaurant -> RestAddress;
    Restaurant -> RestPhone;
    Restaurant -> CuisineType;
    Restaurant -> Rating;

    MenuItem -> ItemID;
    MenuItem -> ItemName;
    MenuItem -> Description;
    MenuItem -> Price;
    MenuItem -> Category;

    Customer -> CustomerID;
    Customer -> CustName;
    Customer -> CustEmail;
    Customer -> CustPhone;
    Customer -> DeliveryAddress;

    Driver -> DriverID;
    Driver -> DriverName;
    Driver -> DriverPhone;
    Driver -> VehicleInfo;
    Driver -> AvailabilityStatus;

    Order -> OrderID;
    Order -> OrderTime;
    Order -> OrderAddress;
    Order -> TotalAmount;
    Order -> OrderStatus;

    OrderItem -> Quantity;

    // Entity-Relationship connections
    Restaurant -> Offers [label="1"];
    Offers -> MenuItem [label="M"];

    Customer -> Places [label="1"];
    Places -> Order [label="M"];

    Driver -> Delivers [label="1"];
    Delivers -> Order [label="M"];

    Order -> Contains [label="M"];
    Contains -> OrderItem [label="N"];
    MenuItem -> OrderItem [label="1"];

    // Primary key indicators
    RestaurantID [style="filled", fillcolor="lightgray"];
    ItemID [style="filled", fillcolor="lightgray"];
    CustomerID [style="filled", fillcolor="lightgray"];
    DriverID [style="filled", fillcolor="lightgray"];
    OrderID [style="filled", fillcolor="lightgray"];
}
```

### Crows Foot diagram

```{mermaid}
erDiagram
    RESTAURANT {
        int Restaurant_ID PK
        string Name
        string Address
        string Phone
        string Cuisine_Type
        decimal Rating
    }

    MENUITEM {
        int Item_ID PK
        string Name
        string Description
        decimal Price
        string Category
        int Restaurant_ID FK
    }

    CUSTOMER {
        int Customer_ID PK
        string Name
        string Email
        string Phone
        string Delivery_Address
    }

    DRIVER {
        int Driver_ID PK
        string Name
        string Phone
        string Vehicle_Info
        string Availability_Status
    }

    ORDER {
        int Order_ID PK
        datetime Order_Time
        string Delivery_Address
        decimal Total_Amount
        string Status
        int Customer_ID FK
        int Driver_ID FK
    }

    ORDERITEM {
        int Order_ID FK
        int Item_ID FK
        int Quantity
    }

    RESTAURANT ||--o{ MENUITEM : "offers"
    CUSTOMER ||--o{ ORDER : "places"
    DRIVER ||--o{ ORDER : "delivers"
    ORDER ||--o{ ORDERITEM : "contains"
    MENUITEM ||--o{ ORDERITEM : "included_in"
```

### Relational model

The ER model translates to the following relational schema:

- **Restaurant**(Restaurant_ID, Name, Address, Phone, Cuisine_Type, Rating)
- **MenuItem**(Item_ID, Name, Description, Price, Category, Restaurant_ID)
- **Customer**(Customer_ID, Name, Email, Phone, Delivery_Address)
- **Driver**(Driver_ID, Name, Phone, Vehicle_Info, Availability_Status)
- **Order**(Order_ID, Order_Time, Delivery_Address, Total_Amount, Status, Customer_ID, Driver_ID)
- **OrderItem**(Order_ID, Item_ID, Quantity)

**Foreign Key Constraints:**
- MenuItem.Restaurant_ID references Restaurant.Restaurant_ID
- Order.Customer_ID references Customer.Customer_ID
- Order.Driver_ID references Driver.Driver_ID
- OrderItem.Order_ID references Order.Order_ID
- OrderItem.Item_ID references MenuItem.Item_ID

## Model 3 - University Course Registration System

Consider a university course registration system that manages students, courses, professors, and enrollment records. The system handles course offerings across different semesters, student registrations, and academic records. Students can enroll in multiple courses each semester, and courses can have multiple students enrolled. Professors teach courses and may teach multiple courses per semester. The system tracks enrollment dates, grades, and semester information.

Each student has a unique student ID, name, email, major, and graduation year. Courses have course codes, titles, credit hours, and descriptions. Professors have professor IDs, names, departments, email addresses, and office locations. Semesters are identified by year and term (Fall, Spring, Summer). Enrollments capture the relationship between students and course offerings, including enrollment date, final grade, and status (enrolled, completed, dropped). Course offerings represent specific instances of courses taught in particular semesters by specific professors.

### Design choices

I identified five main entities for this system: **Student**, **Course**, **Professor**, **Semester**, **CourseOffering**, and **Enrollment**.

**Entity Selection Rationale:**
- **Student**: Represents university students with academic information and contact details
- **Course**: Represents the catalog of courses available, with course codes and descriptions
- **Professor**: Faculty members who teach courses, with departmental and contact information
- **Semester**: Time periods when courses are offered (Fall 2024, Spring 2025, etc.)
- **CourseOffering**: Specific instances of courses taught in particular semesters by specific professors (resolves the many-to-many relationship between Course, Professor, and Semester)
- **Enrollment**: Junction entity that captures student registrations in course offerings with grades and status

**Relationship Design:**
- **Teaches** (Professor-CourseOffering): One-to-many relationship since professors can teach multiple course offerings, but each offering is taught by one professor. Partial participation on Professor side (professors may not teach every semester)
- **OfferedIn** (Course-CourseOffering): One-to-many relationship since courses can be offered multiple times, but each offering is of one specific course. Partial participation on Course side (not all courses are offered every semester)
- **ScheduledIn** (Semester-CourseOffering): One-to-many relationship since semesters can have multiple course offerings, but each offering occurs in one semester. Total participation on CourseOffering side
- **Enrolls** (Student-CourseOffering through Enrollment): Many-to-many relationship resolved through Enrollment entity, allowing students to enroll in multiple offerings and offerings to have multiple students. Partial participation on both sides

### Chen Diagram

```{dot}
//| fig-width: 16
//| fig-height: 12
digraph ER {
    rankdir=TB;
    node [shape=box, style=rounded];

    // Entities
    Student [shape=box, label="Student"];
    Course [shape=box, label="Course"];
    Professor [shape=box, label="Professor"];
    Semester [shape=box, label="Semester"];
    CourseOffering [shape=box, label="CourseOffering"];
    Enrollment [shape=box, label="Enrollment"];

    // Attributes for Student
    StudentID [shape=ellipse, label="Student ID"];
    StudName [shape=ellipse, label="Name"];
    StudEmail [shape=ellipse, label="Email"];
    Major [shape=ellipse, label="Major"];
    GradYear [shape=ellipse, label="Graduation Year"];

    // Attributes for Course
    CourseCode [shape=ellipse, label="Course Code"];
    CourseTitle [shape=ellipse, label="Title"];
    CreditHours [shape=ellipse, label="Credit Hours"];
    CourseDesc [shape=ellipse, label="Description"];

    // Attributes for Professor
    ProfessorID [shape=ellipse, label="Professor ID"];
    ProfName [shape=ellipse, label="Name"];
    Department [shape=ellipse, label="Department"];
    ProfEmail [shape=ellipse, label="Email"];
    OfficeLocation [shape=ellipse, label="Office Location"];

    // Attributes for Semester
    SemesterID [shape=ellipse, label="Semester ID"];
    Year [shape=ellipse, label="Year"];
    Term [shape=ellipse, label="Term"];

    // Attributes for CourseOffering
    OfferingID [shape=ellipse, label="Offering ID"];
    Section [shape=ellipse, label="Section"];
    Schedule [shape=ellipse, label="Schedule"];
    Classroom [shape=ellipse, label="Classroom"];

    // Attributes for Enrollment
    EnrollmentDate [shape=ellipse, label="Enrollment Date"];
    FinalGrade [shape=ellipse, label="Final Grade"];
    Status [shape=ellipse, label="Status"];

    // Relationships
    Teaches [shape=diamond, label="Teaches"];
    OfferedAs [shape=diamond, label="Offered As"];
    ScheduledIn [shape=diamond, label="Scheduled In"];
    EnrollsIn [shape=diamond, label="Enrolls In"];

    // Entity-Attribute connections
    Student -> StudentID;
    Student -> StudName;
    Student -> StudEmail;
    Student -> Major;
    Student -> GradYear;

    Course -> CourseCode;
    Course -> CourseTitle;
    Course -> CreditHours;
    Course -> CourseDesc;

    Professor -> ProfessorID;
    Professor -> ProfName;
    Professor -> Department;
    Professor -> ProfEmail;
    Professor -> OfficeLocation;

    Semester -> SemesterID;
    Semester -> Year;
    Semester -> Term;

    CourseOffering -> OfferingID;
    CourseOffering -> Section;
    CourseOffering -> Schedule;
    CourseOffering -> Classroom;

    Enrollment -> EnrollmentDate;
    Enrollment -> FinalGrade;
    Enrollment -> Status;

    // Entity-Relationship connections
    Professor -> Teaches [label="1"];
    Teaches -> CourseOffering [label="M"];

    Course -> OfferedAs [label="1"];
    OfferedAs -> CourseOffering [label="M"];

    Semester -> ScheduledIn [label="1"];
    ScheduledIn -> CourseOffering [label="M"];

    Student -> EnrollsIn [label="M"];
    EnrollsIn -> Enrollment [label="N"];
    CourseOffering -> Enrollment [label="1"];

    // Primary key indicators
    StudentID [style="filled", fillcolor="lightgray"];
    CourseCode [style="filled", fillcolor="lightgray"];
    ProfessorID [style="filled", fillcolor="lightgray"];
    SemesterID [style="filled", fillcolor="lightgray"];
    OfferingID [style="filled", fillcolor="lightgray"];
}
```

### Crows Foot diagram

```{mermaid}
erDiagram
    STUDENT {
        int Student_ID PK
        string Name
        string Email
        string Major
        int Graduation_Year
    }

    COURSE {
        string Course_Code PK
        string Title
        int Credit_Hours
        string Description
    }

    PROFESSOR {
        int Professor_ID PK
        string Name
        string Department
        string Email
        string Office_Location
    }

    SEMESTER {
        int Semester_ID PK
        int Year
        string Term
    }

    COURSEOFFERING {
        int Offering_ID PK
        string Section
        string Schedule
        string Classroom
        string Course_Code FK
        int Professor_ID FK
        int Semester_ID FK
    }

    ENROLLMENT {
        int Student_ID FK
        int Offering_ID FK
        date Enrollment_Date
        string Final_Grade
        string Status
    }

    COURSE ||--o{ COURSEOFFERING : "offered_as"
    PROFESSOR ||--o{ COURSEOFFERING : "teaches"
    SEMESTER ||--o{ COURSEOFFERING : "scheduled_in"
    STUDENT ||--o{ ENROLLMENT : "enrolls"
    COURSEOFFERING ||--o{ ENROLLMENT : "has_enrollment"
```

### Relational model

The ER model translates to the following relational schema:

- **Student**(Student_ID, Name, Email, Major, Graduation_Year)
- **Course**(Course_Code, Title, Credit_Hours, Description)
- **Professor**(Professor_ID, Name, Department, Email, Office_Location)
- **Semester**(Semester_ID, Year, Term)
- **CourseOffering**(Offering_ID, Section, Schedule, Classroom, Course_Code, Professor_ID, Semester_ID)
- **Enrollment**(Student_ID, Offering_ID, Enrollment_Date, Final_Grade, Status)

**Foreign Key Constraints:**
- CourseOffering.Course_Code references Course.Course_Code
- CourseOffering.Professor_ID references Professor.Professor_ID
- CourseOffering.Semester_ID references Semester.Semester_ID
- Enrollment.Student_ID references Student.Student_ID
- Enrollment.Offering_ID references CourseOffering.Offering_ID


# Reflection

What was the most surprising (good or bad) thing that you learned by doing this assignment?
: The most surprising thing I learned was how different Chen notation and Crow's Foot notation can make the same relationships appear. While Chen diagrams are more explicit about showing relationship entities and cardinalities, Crow's Foot diagrams are much more intuitive for understanding the actual database structure. I also found it challenging to decide when to create separate entities versus when to use attributes, particularly for the CourseOffering entity in the university system - it wasn't immediately obvious that this needed to be a separate entity to properly handle the many-to-many-to-many relationship between Course, Professor, and Semester.

How much time did you spend on this assignment?
: I spent approximately 4-5 hours on this assignment. The majority of time was spent on designing the ER models and ensuring the relationships were correctly represented in both notations. Learning the Graphviz and Mermaid syntax took some time initially, but once I understood the patterns, creating the diagrams became more straightforward. The most time-consuming part was thinking through the design decisions and making sure the cardinalities and participation constraints were logical and realistic.

What CS classes have you had (or have now) that the quarto math expressions and diagrams (e.g., graphviz and mermaid) would have made it easier?
: These tools would have been incredibly useful in several previous classes. In Data Structures and Algorithms, Graphviz would have made it much easier to visualize tree structures, graph algorithms, and algorithm flowcharts instead of drawing them by hand or using basic drawing tools. In Software Engineering, both tools would have been perfect for creating UML diagrams, system architecture diagrams, and workflow charts. Even in Computer Systems Organization, these tools could have helped visualize memory layouts, CPU architectures, and data flow diagrams. The ability to generate professional-looking diagrams from code is much more maintainable than traditional drawing tools, especially when designs need to be updated frequently.



# README

A quality README is an important part of EVERY project. Using the Quarto *include* command we're including a copy of your README in the project report so that a human can evaluate it.

Make sure that you edit the README so that it's explanatory!  Note that you don't need a readme within the *reports* folder for this assignment. We're only
focused on the root *README.md*.

[Here is some info](https://www.freecodecamp.org/news/how-to-write-a-good-readme-file/) on how to write a good README!

::: {style="background:lightgray; margin-left:20px; border-top: 3px solid black; border-bottom: 3px solid black; padding-left:20px; padding-right:20px"}
{{< include ../README.md >}}
:::


