---
title: Homework 3
date: last-modified
author:
    - name: <PERSON><PERSON>
      email: <PERSON>hara<PERSON><EMAIL>
---

Link to my GITHUB Repo: <https://github.com/cmsc-vcu/cmsc408-fa2025-hw3-<PERSON>ayDharamsi>

(Add introduction section here.  What is this homework about?  What are you about to do?)

# Easter Egg

The following examples demonstrate how one can use Quarto to write equations and formulas.  Quarto
has a built-in $\LaTeX$ engine, plus other formula and greek letter engines.  Enjoy!

DELETE THIS EASTER EGG SECTION. I included it for examples, but it is not directly
related to this homework assignment.

These examples (source QMD and rendered HTML) can be found in the ../examples folder.

* [Example 1](../examples/example-1.html) - contains references, sample symbols, and a scaffold for a study guide that you might find useful.

* [Example 2](../examples/example-2.html) - this is an example document using $\LaTeX$ formulas in a quarto QMD file.

* [Example 3](../examples/example-3.html) - this document contains a proof for the Pythagorean Theorem.

* [Example 4](../examples/example-4.html) - this document contains a proof for the commutativity of the selection and join operators.

# Models

Below are three entity-relation models for systems in the world around me.

## Model 1 - ....(title)...

(description of system as show in examples)

### Design choices

(Talk about why you picked the entities that you did.  Describe why/how you picked the
relations that you did.  Focus on cardinality and participation.)

### Chen Diagram

(chen diagram here)

### Crows Foot diagram

(crows-foot diagram here)

### Relational model

(relations and attributes here)

## Model 2 - ....(title)...

(description of system as show in examples)

### Design choices

(Talk about why you picked the entities that you did.  Describe why/how you picked the
relations that you did.  Focus on cardinality and participation.)

### Chen Diagram

(chen diagram here)

### Crows Foot diagram

(crows-foot diagram here)

### Relational model

(relations and attributes here)

## Model 3 - ....(title)...

(description of system as show in examples)

### Design choices

(Talk about why you picked the entities that you did.  Describe why/how you picked the
relations that you did.  Focus on cardinality and participation.)

### Chen Diagram

(chen diagram here)

### Crows Foot diagram

(crows-foot diagram here)

### Relational model

(relations and attributes here)


# Reflection

What was the most surprising (good or bad) thing that you learned by doing this assignment?
: (answer here)

How much time did you spend on this assignment?
: (answer here)

What CS classes have you had (or have now) that the quarto math expressions and diagrams (e.g., graphviz and mermaid) would have made it easier?
: (answer here)



# README

A quality README is an important part of EVERY project. Using the Quarto *include* command we're including a copy of your README in the project report so that a human can evaluate it.

Make sure that you edit the README so that it's explanatory!  Note that you don't need a readme within the *reports* folder for this assignment. We're only
focused on the root *README.md*.

[Here is some info](https://www.freecodecamp.org/news/how-to-write-a-good-readme-file/) on how to write a good README!

::: {style="background:lightgray; margin-left:20px; border-top: 3px solid black; border-bottom: 3px solid black; padding-left:20px; padding-right:20px"}
{{< include ../README.md >}}
:::


