[![Review Assignment Due Date](https://classroom.github.com/assets/deadline-readme-button-22041afd0340ce965d47ae6ef1cefeee28c7c493a6346c4f15d667ab976d596c.svg)](https://classroom.github.com/a/0ydrtIhk)

# CMSC 408 Homework 3: Entity-Relationship Modeling

## Overview

This repository contains my submission for Homework 3 in CMSC 408 (Database Systems). The assignment focuses on creating custom real-world scenarios and modeling them using Entity-Relationship (ER) diagrams.

## Assignment Description

This homework requires creating **three unique real-world scenarios** that demonstrate database modeling concepts. For each scenario, I have:

1. **Described the system** - A detailed explanation of the real-world system being modeled
2. **Created Chen ER diagrams** - Using Graphviz to show entities, attributes, and relationships in Chen notation
3. **Created Crow's Foot ER diagrams** - Using Mermaid to show the same models in Crow's Foot notation
4. **Developed relational schemas** - Converting the ER models into relation set schemas
5. **Documented design choices** - Explaining entity selection, relationship cardinality, and participation decisions

## Scenarios Covered

1. **Library Management System** - Managing books, members, authors, and loan transactions
2. **Online Food Delivery System** - Coordinating restaurants, customers, orders, and delivery drivers
3. **University Course Registration System** - Handling students, courses, professors, and enrollments

## Repository Structure

- `reports/report.qmd` - Main Quarto document containing all scenarios and diagrams
- `reports/report.html` - Rendered HTML report (generated from QMD file)
- `examples/` - Reference examples and documentation
- `README.md` - This file

## How to Run

To regenerate the report:

1. Ensure you have Quarto installed
2. Navigate to the `reports/` directory
3. Run: `quarto render report.qmd`
4. Open `report.html` in your browser

## Tools Used

- **Quarto** - For document creation and rendering
- **Graphviz** - For Chen ER diagrams
- **Mermaid** - For Crow's Foot ER diagrams
- **LaTeX** - For mathematical notation and formatting

## Author

Sunay Dharamsi  
Email: <EMAIL>  
Course: CMSC 408 - Database Systems  
Semester: Fall 2025
